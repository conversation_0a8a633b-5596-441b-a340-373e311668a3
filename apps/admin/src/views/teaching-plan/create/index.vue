<script setup lang="ts">
import MDEditor from '@sa/components/common/md-editor/MDEditor.vue'
import BackButton from '@sa/components/common/back-button.vue'
import LeftView from './components/left-view/index.vue'

const markdown = ref('纳米好啊\n\n')
</script>

<template>
  <div class="relative h-full w-full flex py-16px pt-64px">
    <BackButton />

    <!-- 背景 -->
    <div class="fixed inset-0 z-0 h-full">
      <div class="absolute inset-0 h-full w-full bg-[#F2F6FC]" />
      <div
        class="absolute inset-0 h-full w-full from-[#8AD1F9] via-[#F3F6FF] to-[#ECD9FF] bg-gradient-to-b opacity-80"
      />
    </div>

    <div class="h-full w-full flex">
      <NScrollbar class="h-full pr-16px">
        <NCard class="h-full w-fit shrink-0">
          <LeftView />
        </NCard>
      </NScrollbar>
      <div class="h-full flex-1 overflow-hidden rounded-16px">
        <MDEditor v-model:value="markdown" />
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
