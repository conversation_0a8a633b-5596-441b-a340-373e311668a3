<script setup lang="ts">
import LeftView from './components/left-view/index.vue'
import RightView from './components/right-view/index.vue'

defineOptions({
  name: 'Questions',
})
</script>

<template>
  <div class="relative h-full w-full flex-col-stretch">
    <!-- 背景 -->
    <!-- <div class="bg-container-view absolute inset-0 z-0 h-full" /> -->
    <!-- <div class="absolute inset-0 z-0 h-full">
      <div class="absolute inset-0 h-full w-full bg-[#F2F6FC]" />
      <div
        class="absolute inset-0 h-full w-full from-[#8AD1F9] via-[#F3F6FF] to-[#ECD9FF] bg-gradient-to-b opacity-80"
      />
    </div> -->
    <div class="h-full w-full flex bg-white p-12px">
      <LeftView class="w-40%" />
      <RightView class="h-full w-60%" />
    </div>
    <!-- <NCard :bordered="false" size="small" class="h-full card-wrapper"> -->
    <!-- <NGrid class="h-full" responsive="screen" :x-gap="20" :y-gap="20" item-responsive>
      <NGridItem span="24 l:8">
        <LeftView />
      </NGridItem>
      <NGridItem span="24 l:16">
        <RightView class="h-full" />
      </NGridItem>
    </NGrid> -->
    <!-- </NCard> -->
  </div>
</template>

<style lang="scss">
// .bg-container-view{
//   background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGa2cc791875d5eb4639bbba2971f330fc.png) 100% no-repeat;
//   background-size: 100% 100%;
// }
</style>
