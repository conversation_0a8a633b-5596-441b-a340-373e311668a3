<script setup lang="ts">
defineOptions({
  name: 'RightView',
})

// 创建方式选项
</script>

<template>
  <div class="h-full w-full flex-center bg-white p-12px">
    <div class="text-group_3 empty flex-col items-center">
      <img src="@/assets/imgs/empty.png" alt="" class="h-272px w-272px">
      <span class="text-center text-20px">快速生成教案指南</span>
      <span class="mt-14px text-center text-18px text-[rgba(172,172,172,1)] font-normal">1.选择创建方式，填好必要信息<br>2.系统将自动保存您的设置<br>3.&nbsp;点击”一键生成教案”，教案内容将显示在右侧<br>4.选中教案内容，可对其进行删除、润色、扩写等操作</span>
    </div>
  </div>
</template>
