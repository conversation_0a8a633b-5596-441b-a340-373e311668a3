<script setup lang="ts">
import { computed } from 'vue'
import { useResetReactive } from '@sa/hooks'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { getServiceBaseURL } from '@sa/utils'
import TitleForm from './components/title-form.vue'
import TextForm from './components/text-form.vue'
import ChapterForm from './components/chapter-form.vue'
import DocumentForm from './components/document-form.vue'
import { useAuthStore } from '@/store/modules/auth'
import { getAgentModelInfo, getDifficulty, getLearningLevel, getQuestionTypes } from '@/service/api'

defineOptions({
  name: 'LeftView',
})
// 环境配置
const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y'
const { baseURL } = getServiceBaseURL(import.meta.env, isHttpProxy)
// 获取认证store
const authStore = useAuthStore()
const { token } = authStore
// 创建方式选项
const createMethods = [
  { key: 'title', label: '知识点出题 ', active: true, mode: 1 },
  { key: 'text', label: '文本出题', active: false, mode: 2 },
  { key: 'chapter', label: '附件出题', active: false, mode: 3 },
  { key: 'document', label: '章节出题', active: false, mode: 4 },
]

// AI模型选择选项
const modelOptions = ref([] as QuestionsApi.AgentModelInfoResponse[])

// 使用 useResetReactive 创建可重置的表单数据
const [formModel, resetFormModel] = useResetReactive({
  AdditionalRequirements: '', // 补充内容（出题要求）
  AIModeId: '', // AI模型ID
  ChapterName: '',
  DifficultyLevelName: '', // 难度等级名称
  FileUrl: '', // 文件URL
  Grade: 1, // 年级
  KnowledgePointIds: [], // 知识点ID列表
  Mode: 1, // 出题模式
  QuestionCount: 5, // 出题数量
  QuestionDirectionName: '', // 出题方向名称
  QuestionTypeIds: [], // 题型ID列表
  TextContent: '', // 文本内容
})

// 创建标题表单的计算属性用于双向绑定
const titleFormModel = computed({
  get: () => ({
    KnowledgePointIds: formModel.KnowledgePointIds,
    AdditionalRequirements: formModel.AdditionalRequirements,
  }),
  set: (value) => {
    console.log(value)
    formModel.KnowledgePointIds = value.KnowledgePointIds
    formModel.AdditionalRequirements = value.AdditionalRequirements
  },
})

// 处理模型选择
function handleModelSelect(key: string) {
  formModel.AIModeId = key
}
// 处理创建方式切换
function switchCreateMethod(key: number) {
  // 保存当前的模型ID
  const currentModelId = formModel.AIModeId
  resetFormModel()
  formModel.Mode = key
  // 恢复模型ID
  formModel.AIModeId = currentModelId
}

function generateLesson() {
  fetchEventSource(`${baseURL}/AgentIntelligentQuestion/AgentIntelligentQuestion/GenerateQuestionsStream`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(formModel),
    openWhenHidden: true,
    onopen: async (response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
    },
    onmessage: async (msg) => {
      try {
        const { Content, Success } = JSON.parse(msg.data)
        console.log(msg.data)
      }
      catch (error) {
        console.error('解析SSE消息失败:', error)
      }
    },
    onclose: () => {
    },
    onerror: (err) => {
      throw err
    },
  })
}
const questionTypeOptions = ref([] as QuestionsApi.GetQuestionTypesResponse[])
const difficultyOptions = ref([] as QuestionsApi.GetDifficultyResponse[])
const learningLevelOptions = ref([] as QuestionsApi.GetLearningLevelResponse[])
function initData() {
  getAgentModelInfo().then((res) => {
    if (res.data) {
      modelOptions.value = res.data
      formModel.AIModeId = res.data[0].Id
    }
  })
  getQuestionTypes().then((res) => {
    if (res.data) {
      questionTypeOptions.value = res.data
    }
  })
  getDifficulty({
    grade: 1,
    year: '2023',
  }).then((res) => {
    if (res.data) {
      difficultyOptions.value = res.data
      console.log('difficultyOptions loaded:', res.data)
    }
  })
  getLearningLevel({
    grade: 1,
    year: '2023',
  }).then((res) => {
    if (res.data) {
      learningLevelOptions.value = res.data
    }
  })
}

// 监控 DifficultyLevelName 的变化
watch(() => formModel.DifficultyLevelName, (newVal, oldVal) => {
  console.log('DifficultyLevelName changed from:', oldVal, 'to:', newVal)
}, { immediate: true })

onMounted(() => {
  initData()
})
</script>

<template>
  <div class="h-full flex flex-col">
    <div class="shrink-0">
      <!-- 标题栏 -->
      <div class="mb-16px flex items-center justify-between">
        <div class="flex items-center gap-8px">
          <div class="h-38px w-38px flex items-center justify-center rounded-8px from-blue-500 to-purple-500">
            <SvgIcon icon="mdi:robot-outline" class="text-20px text-white" />
          </div>
          <span class="from-blue-500 to-purple-500 text-22px font-600">
            AI智能命题
          </span>
        </div>

        <!-- AI模型选择下拉框 -->
        <NDropdown
          trigger="click"
          key-field="Id"
          label-field="ModelName"
          :options="modelOptions"
          :show-arrow="true"
          @select="handleModelSelect"
        >
          <NButton quaternary class="flex items-center gap-6px rounded-8px bg-blue-100 px-12px py-8px hover:bg-blue-200">
            <SvgIcon icon="mdi:brain" class="mr-4px text-16px text-blue-600" />
            <span class="text-14px text-blue-700">{{ formModel.Mode ? modelOptions.find(m => m.Id === formModel.AIModeId)?.ModelName : '选择模型' }}</span>
            <SvgIcon icon="mdi:chevron-down" class="text-12px text-blue-600" />
          </NButton>
        </NDropdown>
      </div>

      <!-- 创建方式选项卡 -->
      <div class="mb-16px flex justify-between rounded-8px bg-blue-100 p-4px">
        <div
          v-for="method in createMethods"
          :key="method.key"
          class="cursor-pointer rounded-6px px-16px py-8px text-14px font-500 transition-all duration-200"
          :class="[
            formModel.Mode === method.mode
              ? 'bg-white text-blue-600 shadow-sm '
              : ' hover:text-blue-600',
          ]"
          @click="switchCreateMethod(method.mode)"
        >
          {{ method.label }}
        </div>
      </div>
    </div>
    <!-- 内容区域 -->
    <div class="min-h-0 flex-1">
      <NScrollbar class="h-full">
        <!-- 根据创建方式渲染不同的表单组件 -->
        <TitleForm
          v-if="formModel.Mode === 1"
          v-model:model-info="titleFormModel"
        />
        <!-- 文本出题 -->
        <TextForm
          v-if="formModel.Mode === 2"
          v-model:text-content="formModel.TextContent"
        />
        <!-- 附件出题 -->
        <ChapterForm
          v-if="formModel.Mode === 3"
          :model-value="{
            attachments: [],
            questionType: formModel.QuestionTypeIds?.join(',') || '',
            difficulty: formModel.DifficultyLevelName,
            questionCount: formModel.QuestionCount,
            extractMethod: String(formModel.Mode),
            otherRequirements: formModel.AdditionalRequirements,
          }"
        />

        <DocumentForm
          v-if="formModel.Mode === 4"
          :model-value="{
            subject: formModel.ChapterName,
            chapter: formModel.ChapterName,
            section: formModel.ChapterName,
            questionType: formModel.QuestionTypeIds?.join(',') || '',
            difficulty: formModel.DifficultyLevelName,
            questionCount: formModel.QuestionCount,
            knowledgePoints: formModel.KnowledgePointIds,
            otherRequirements: formModel.AdditionalRequirements,
          }"
        />
      </NScrollbar>
    </div>

    <!-- 生成按钮区域 -->
    <div class="shrink-0 rounded-8px bg-blue-50 p-16px">
      <div class="mb-24px">
        <NGrid :cols="2" :x-gap="12" y-gap="14px">
          <NGridItem>
            <NSelect v-model:value="formModel.QuestionTypeIds" label-field="Name" value-field="Id" clearable multiple :options="questionTypeOptions" placeholder="选择题目的题型" />
          </NGridItem>
          <NGridItem>
            <NSelect
              v-model:value="formModel.DifficultyLevelName"
              label-field="text"
              value-field="value"
              :options="difficultyOptions"
              placeholder="请选择题目难度"
              :loading="difficultyOptions.length === 0"
            />
          </NGridItem>
          <NGridItem>
            <NSelect v-model:value="formModel.QuestionDirectionName" label-field="Text" value-field="Text" :options="learningLevelOptions" placeholder="请选择题目方向" />
          </NGridItem>
          <NGridItem>
            <NInput
              :value="String(formModel.QuestionCount)"
              placeholder="请输入出题数量"
              @update:value="(value) => formModel.QuestionCount = Number(value) || 0"
            />
          </NGridItem>
        </NGrid>
      </div>

      <div class="flex-col items-center justify-center">
        <NButton
          type="primary"
          size="large"
          class="mb-12px from-blue-500 to-purple-500 bg-gradient-to-r px-24px py-12px"
          @click="generateLesson"
        >
          <template #icon>
            <SvgIcon icon="mdi:magic-staff" />
          </template>
          一键生成教案
        </NButton>
        <p class="text-12px text-gray-500">
          内容由AI生成，仅供参考。
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
