<script setup lang="ts">
defineOptions({
  name: 'ChapterForm',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

interface Props {
  modelValue: {
    attachments: File[]
    questionType: string
    difficulty: string
    questionCount: number
    extractMethod: string
    otherRequirements: string
  }
}

interface Emits {
  (e: 'update:modelValue', value: Props['modelValue']): void
}

// 计算属性用于双向绑定
const formData = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 处理文件上传
function handleFileChange(files: File[]) {
  formData.value = {
    ...formData.value,
    attachments: files,
  }
}

// 处理文件移除
function handleFileRemove(index: number) {
  const attachments = [...formData.value.attachments]
  attachments.splice(index, 1)
  formData.value = {
    ...formData.value,
    attachments,
  }
}
</script>

<template>
  <div>
    <!-- 文件上传 -->
    <NFormItem label="上传附件" path="attachments" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">上传附件</span>
      </template>
      <NUpload
        multiple
        :max="5"
        accept=".pdf,.doc,.docx,.txt"
        @update:file-list="handleFileChange"
      >
        <NUploadDragger>
          <div class="mb-12px flex items-center justify-center gap-8px">
            <SvgIcon icon="mdi:cloud-upload" class="text-48px text-gray-400" />
            <NText class="text-16px">
              点击或者拖动文件到该区域来上传
            </NText>
          </div>

          <p depth="3" class="">
            最多上传 3 个文件
          </p>
          <p depth="3" class="">
            支持上传图片、Word、PDF 格式文件，单个文件大小限制 20MB 以内
          </p>
          <p depth="3" class="">
            文件解析需耗时，提交后请耐心等待，勿重复操作
          </p>
        </NUploadDragger>
      </NUpload>
    </NFormItem>

    <!-- 已上传文件列表 -->
    <div v-if="formData.attachments.length > 0" class="mb-16px">
      <div class="mb-8px text-14px text-[#464646] font-500">
        已上传文件：
      </div>
      <div class="space-y-8px">
        <div
          v-for="(file, index) in formData.attachments"
          :key="index"
          class="flex items-center justify-between rounded-6px bg-gray-50 p-8px"
        >
          <div class="flex items-center gap-8px">
            <SvgIcon icon="mdi:file-document" class="text-16px text-blue-500" />
            <span class="text-14px">{{ file.name }}</span>
          </div>
          <NButton
            size="small"
            quaternary
            type="error"
            @click="handleFileRemove(index)"
          >
            <SvgIcon icon="mdi:close" />
          </NButton>
        </div>
      </div>
    </div>

    <!-- 其他要求 -->
    <NFormItem label="其他要求" path="otherRequirements">
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">补充内容（选填）</span>
      </template>
      <NInput
        v-model:value="formData.otherRequirements"
        type="textarea"
        placeholder="请输入其他特殊要求（可选）"
        :rows="3"
        clearable
        maxlength="500"
        show-count
      />
    </NFormItem>
  </div>
</template>
