<script setup lang="ts">
defineOptions({
  name: 'DocumentForm',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

interface Props {
  modelValue: {
    subject: string
    chapter: string
    section: string
    questionType: string
    difficulty: string
    questionCount: number
    knowledgePoints: string[]
    otherRequirements: string
  }
}

interface Emits {
  (e: 'update:modelValue', value: Props['modelValue']): void
}

// 学科选项
const subjectOptions = [
  { label: '语文', value: 'chinese' },
  { label: '数学', value: 'math' },
  { label: '英语', value: 'english' },
  { label: '物理', value: 'physics' },
  { label: '化学', value: 'chemistry' },
  { label: '生物', value: 'biology' },
  { label: '历史', value: 'history' },
  { label: '地理', value: 'geography' },
  { label: '政治', value: 'politics' },
]

// 题目类型选项
const questionTypeOptions = [
  { label: '选择题', value: 'choice' },
  { label: '填空题', value: 'blank' },
  { label: '判断题', value: 'judge' },
  { label: '简答题', value: 'short' },
  { label: '综合题', value: 'comprehensive' },
]

// 难度选项
const difficultyOptions = [
  { label: '简单', value: 'easy' },
  { label: '中等', value: 'medium' },
  { label: '困难', value: 'hard' },
]

// 知识点选项（示例）
const knowledgePointOptions = [
  { key: '基础概念', label: '基础概念' },
  { key: '重点难点', label: '重点难点' },
  { key: '实际应用', label: '实际应用' },
  { key: '综合运用', label: '综合运用' },
]

// 计算属性用于双向绑定
const formData = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 处理知识点选择
function toggleKnowledgePoint(key: string) {
  const knowledgePoints = [...formData.value.knowledgePoints]
  const index = knowledgePoints.indexOf(key)

  if (index > -1) {
    knowledgePoints.splice(index, 1)
  }
  else {
    knowledgePoints.push(key)
  }

  formData.value = {
    ...formData.value,
    knowledgePoints,
  }
}
function selectKnowledgePoint() {
  formData.value.knowledgePoints = []
}
</script>

<template>
  <div>
    <!-- 出题范围标题 -->
    <div class="mb-16px flex items-center">
      <SvgIcon icon="mdi:file-document-outline" class="mr-8px text-20px text-blue-500" />
      <span class="text-14px text-[#333] font-500">出题范围</span>
    </div>

    <!-- 知识点选择区域 -->
    <div
      class="mb-24px min-h-200px flex flex-col cursor-pointer items-center justify-center border-2 border-gray-300 rounded-8px border-dashed bg-gray-50 p-24px transition-all duration-200 hover:border-blue-400 hover:bg-blue-50"
      @click="selectKnowledgePoint"
    >
      <div class="h-full flex flex-col items-center justify-center">
        <!-- 加号图标 -->
        <div class="mb-12px h-48px w-48px flex items-center justify-center rounded-full bg-blue-500 text-white">
          <SvgIcon icon="mdi:plus" class="text-24px" />
        </div>
        <!-- 提示文字 -->
        <span class="text-14px text-gray-600 font-500">选择本课知识点</span>
      </div>
    </div>

    <!-- 补充内容区域 -->
    <div class="border border-gray-200 rounded-8px bg-white">
      <!-- 标题栏 -->
      <div
        class="flex items-center justify-between p-16px"
      >
        <span class="text-14px text-[#464646] font-500">补充内容（选填）</span>
      </div>

      <!-- 可折叠内容 -->
      <div
        class="overflow-hidden transition-all duration-300"
      >
        <div class="border-t border-gray-200 p-16px">
          <NInput
            v-model:value="formData.otherRequirements"
            type="textarea"
            placeholder="您可以补充特殊的出题要求。&#10;示例如下：&#10;题目中需要包含&quot;折射&quot;&quot;反射&quot;这两个词汇"
            :rows="4"
            clearable
            maxlength="500"
            show-count
            class="w-full"
          />
        </div>
      </div>
    </div>
  </div>
</template>
