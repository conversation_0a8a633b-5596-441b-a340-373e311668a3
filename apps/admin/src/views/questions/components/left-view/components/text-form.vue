<script setup lang="ts">
defineOptions({
  name: 'TextForm',
})
const textContent = defineModel('textContent', {
  type: String,
  default: '',
})
</script>

<template>
  <div class="h-full">
    <div class="mb-16px flex items-center">
      <SvgIcon icon="mdi:file-document-outline" class="mr-8px text-20px text-blue-500" />
      <span class="text-14px text-[#333] font-500">出题范围</span>
    </div>
    <NInput
      v-model:value="textContent"
      type="textarea"
      placeholder="请在下方输入框内 **粘贴或录入文本内容**（如知识点梳理、课文段落、教学案例等），AI将深度解析文本核心信息，自动生成对应题目。  👉 支持场景：概念定义、课文节选、实验描述等教学素材  👉 操作建议：文本越完整清晰，命题精准度越高~  输入完成后，点击「立即出题」即可启动AI命题流程。"
      :rows="6"
      clearable
      maxlength="2000"
      show-count
    />
  </div>
</template>
