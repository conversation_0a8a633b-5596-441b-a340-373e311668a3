import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'agent',
    path: '/agent',
    component: 'layout.base',
    meta: {
      title: '智能体',
      icon: 'ic:round-dashboard',
      order: 1
    },
    children: [
      {
        name: 'agent_evaluation',
        path: '/agent/evaluation',
        component: 'view.agent_evaluation',
        meta: {
          title: '口语交际评估',
          hideInMenu: true,
          order: 2
        }
      },
      {
        name: 'agent_home',
        path: '/agent/home',
        component: 'view.agent_home',
        meta: {
          title: '智能体',
          hideInMenu: true,
          order: 1
        }
      },
      {
        name: 'agent_list',
        path: '/agent/list',
        component: 'view.agent_list',
        meta: {
          title: '智能体列表',
          hideInMenu: true,
          order: 2
        }
      },
      {
        name: 'agent_spoken',
        path: '/agent/spoken',
        component: 'view.agent_spoken',
        meta: {
          title: '口语交际智能体',
          hideInMenu: true,
          order: 3
        }
      },
      {
        name: 'agent_statistics',
        path: '/agent/statistics',
        component: 'view.agent_statistics',
        meta: {
          title: '智能体统计',
          order: 4,
          hideInMenu: true
        }
      }
    ]
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|qrcode-login|reset-pwd)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: '系统管理',
      icon: 'carbon:cloud-service-management',
      order: 9
    },
    children: [
      {
        name: 'manage_dict',
        path: '/manage/dict',
        component: 'view.manage_dict',
        meta: {
          title: '字典管理',
          icon: 'material-symbols:dictionary'
        }
      },
      {
        name: 'manage_menu',
        path: '/manage/menu',
        component: 'view.manage_menu',
        meta: {
          title: '菜单管理',
          icon: 'material-symbols:menu',
          order: 3,
          keepAlive: true
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          title: '角色管理',
          icon: 'carbon:user-role',
          order: 2
        }
      },
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: '用户管理',
          icon: 'ic:round-manage-accounts',
          order: 1
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        meta: {
          title: '用户详情',
          hideInMenu: true,
          activeMenu: 'manage_user'
        }
      }
    ]
  },
  {
    name: 'questions',
    path: '/questions',
    component: 'layout.base$view.questions',
    meta: {
      title: 'AI出题',
      hideInMenu: true
    }
  },
  {
    name: 'teaching-plan',
    path: '/teaching-plan',
    component: 'layout.base',
    meta: {
      title: 'teaching-plan',
      hideInMenu: true
    },
    children: [
      {
        name: 'teaching-plan_create',
        path: '/teaching-plan/create',
        component: 'view.teaching-plan_create',
        meta: {
          title: '教案生成'
        }
      },
      {
        name: 'teaching-plan_home',
        path: '/teaching-plan/home',
        component: 'view.teaching-plan_home',
        meta: {
          title: 'teaching-plan_home'
        }
      }
    ]
  }
];
