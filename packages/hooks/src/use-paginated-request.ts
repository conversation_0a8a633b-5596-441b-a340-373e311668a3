import { computed, onMounted, ref } from 'vue'
import type { Ref } from 'vue'
import type {
  AxiosError,
  FlatResponseData,
} from '@sa/axios'
import useLoading from './use-loading'

/**
 * 分页参数接口
 */
export interface PaginationParams {
  /** 当前页码，从1开始 */
  current: number
  /** 每页大小 */
  pageSize: number
}

/**
 * 分页响应数据接口
 * @template T 列表项数据类型
 */
export interface PaginatedData<T = any> {
  /** 当前页的数据列表 */
  list: T[]
  /** 总数据量 */
  total?: number
  /** 当前页码 */
  current?: number
  /** 每页大小 */
  pageSize?: number
  /** 是否还有更多数据（可选，会自动计算） */
  hasMore?: boolean
}

/**
 * usePaginatedRequest 配置选项
 * @template T 列表项数据类型
 * @template RawData 原始响应数据类型
 */
export interface UsePaginatedRequestOptions<T = any, RawData = any> {
  /** 是否手动请求，默认为 false（即自动请求） */
  manual?: boolean
  /** 默认每页大小 */
  defaultPageSize?: number
  /** 默认起始页码 */
  defaultCurrent?: number
  /** 数据转换函数，用于处理响应数据 */
  transform?: (data: RawData) => PaginatedData<T>
}

/**
 * 分页信息接口
 */
export interface PaginationInfo {
  /** 当前页码 */
  current: number
  /** 每页大小 */
  pageSize: number
  /** 总数据量 */
  total: number
  /** 是否还有更多数据 */
  hasMore: boolean
}

/**
 * usePaginatedRequest 返回值类型
 * @template T 列表项数据类型
 * @template ResponseData 响应数据类型
 */
export interface UsePaginatedRequestResult<T, ResponseData = any> {
  /** 累积的数据列表 */
  data: Ref<T[]>
  /** 首次加载状态 */
  loading: Ref<boolean>
  /** 加载更多状态 */
  loadingMore: Ref<boolean>
  /** 请求错误信息 */
  error: Ref<AxiosError<ResponseData> | null>
  /** 分页信息 */
  pagination: Ref<PaginationInfo>
  /** 重新开始分页（重置到第一页） */
  run: () => Promise<void>
  /** 加载下一页数据 */
  loadMore: () => Promise<void>
  /** 刷新当前页数据 */
  refresh: () => Promise<void>
  /** 重置所有状态 */
  reset: () => void
}

/**
 * 带分页加载更多功能的请求 Hook
 * @template T 列表项数据类型
 * @template ResponseData 响应数据类型
 * @param promiseFunction 返回 Promise<FlatResponseData<PaginatedData<T>>> 的函数
 * @param options 配置选项
 * @returns 返回包含分页数据和操作方法的对象
 *
 * @example
 * ```typescript
 * // 创建请求实例
 * const request = createFlatRequest()
 *
 * // 基本使用
 * const { data, loading, loadingMore, error, pagination, run, loadMore, refresh, reset } = usePaginatedRequest(
 *   (params: PaginationParams) => request({
 *     url: '/api/users',
 *     method: 'GET',
 *     params
 *   })
 * )
 *
 * // 使用 transform 转换数据
 * interface BackendData {
 *   items: User[]
 *   totalCount: number
 *   page: number
 *   size: number
 *   hasNext: boolean
 * }
 *
 * const { data } = usePaginatedRequest<User, any, BackendData>(
 *   (params: PaginationParams) => request({
 *     url: '/api/users',
 *     method: 'GET',
 *     params
 *   }),
 *   {
 *     transform: (rawData: BackendData) => ({
 *       list: rawData.items, // 将 items 字段映射为 list
 *       total: rawData.totalCount, // 将 totalCount 映射为 total
 *       current: rawData.page,
 *       pageSize: rawData.size,
 *       hasMore: rawData.hasNext
 *     })
 *   }
 * )
 *
 * // 加载更多
 * await loadMore()
 *
 * // 刷新
 * await refresh()
 *
 * // 重置
 * reset()
 * ```
 */
export default function usePaginatedRequest<T = any, ResponseData = any, RawData = any>(
  promiseFunction: (params: PaginationParams) => Promise<FlatResponseData<RawData, ResponseData>>,
  options: UsePaginatedRequestOptions<T, RawData> = {},
): UsePaginatedRequestResult<T, ResponseData> {
  const {
    manual = false,
    defaultPageSize = 10,
    defaultCurrent = 1,
    transform,
  } = options

  // 使用 loading hooks
  const { loading, startLoading, endLoading } = useLoading()
  const { loading: loadingMore, startLoading: startLoadingMore, endLoading: endLoadingMore } = useLoading()

  // 累积的数据列表
  const data = ref<T[]>([]) as Ref<T[]>
  // 错误状态
  const error = ref<AxiosError<ResponseData> | null>(null) as Ref<AxiosError<ResponseData> | null>

  // 分页信息
  const paginationInfo = ref<PaginationInfo>({
    current: defaultCurrent,
    pageSize: defaultPageSize,
    total: 0,
    hasMore: false,
  })

  // 计算分页信息的响应式对象
  const pagination = computed(() => paginationInfo.value)

  /**
   * 执行请求的核心方法
   * @param isLoadMore 是否为加载更多操作
   * @param targetPage 目标页码（可选，用于指定请求的页码）
   * @param shouldReset 是否需要重置数据（用于 run 方法）
   */
  const executeRequest = async (isLoadMore = false, targetPage?: number, shouldReset = false): Promise<void> => {
    try {
      // 清除之前的错误
      error.value = null

      // 开始加载
      if (isLoadMore) {
        startLoadingMore()
      }
      else {
        startLoading()
      }

      // 确定请求的页码
      const requestPage = targetPage ?? paginationInfo.value.current

      // 执行请求
      const result = await promiseFunction({
        current: requestPage,
        pageSize: paginationInfo.value.pageSize,
      })

      // 检查请求结果
      if (result.error) {
        error.value = result.error
        return
      }

      const rawData = result.data
      if (rawData) {
        // 如果提供了 transform 函数，则使用它转换数据
        let paginatedData: PaginatedData<T>
        if (transform) {
          paginatedData = transform(rawData)
        }
        else {
          // 如果没有 transform 函数，假设原始数据已经是 PaginatedData 格式
          paginatedData = rawData as unknown as PaginatedData<T>
        }

        // 验证并更新分页信息
        const currentPage = paginatedData.current ?? targetPage ?? paginationInfo.value.current
        const pageSize = paginatedData.pageSize ?? paginationInfo.value.pageSize
        const total = paginatedData.total ?? 0
        const hasMore = paginatedData.hasMore ?? (currentPage * pageSize < total)

        paginationInfo.value = {
          current: currentPage,
          pageSize,
          total,
          hasMore,
        }

        // 更新数据
        if (isLoadMore) {
          // 加载更多：追加数据
          data.value = [...data.value, ...paginatedData.list]
        }
        else if (shouldReset) {
          // 重新开始分页：替换数据（用于 run 方法）
          data.value = paginatedData.list
        }
        else {
          // 刷新当前页：替换数据
          data.value = paginatedData.list
        }
      }
    }
    catch (err) {
      error.value = err as AxiosError<ResponseData>
    }
    finally {
      if (isLoadMore) {
        endLoadingMore()
      }
      else {
        endLoading()
      }
    }
  }

  /**
   * 重新开始分页（重置到第一页）
   */
  const run = async (): Promise<void> => {
    // 不提前修改状态，而是传递目标页码和重置标志给 executeRequest
    // 如果请求成功，executeRequest 会根据响应数据更新状态并重置数据
    // 如果请求失败，原有状态保持不变
    await executeRequest(false, defaultCurrent, true)
  }

  /**
   * 加载下一页数据
   */
  const loadMore = async (): Promise<void> => {
    if (!paginationInfo.value.hasMore || loadingMore.value) {
      return
    }

    // 计算下一页页码，但不提前修改状态
    const nextPage = paginationInfo.value.current + 1
    await executeRequest(true, nextPage)
  }

  /**
   * 刷新当前页数据
   */
  const refresh = async (): Promise<void> => {
    await executeRequest(false)
  }

  /**
   * 重置所有状态
   */
  const reset = (): void => {
    data.value = []
    error.value = null
    paginationInfo.value = {
      current: defaultCurrent,
      pageSize: defaultPageSize,
      total: 0,
      hasMore: false,
    }
  }

  // 如果不是手动模式，则在组件挂载时自动执行请求
  if (!manual) {
    onMounted(() => {
      run()
    })
  }

  return {
    data,
    loading,
    loadingMore,
    error,
    pagination,
    run,
    loadMore,
    refresh,
    reset,
  }
}
