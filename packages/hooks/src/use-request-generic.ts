import { ref } from 'vue'
import type { Ref } from 'vue'
import type {
  AxiosError,
  FlatResponseData,
} from '@sa/axios'
import useLoading from './use-loading'

/**
 * useRequest 配置选项
 */
export interface UseRequestOptions {
  /** 是否手动请求，默认为 false（即自动请求） */
  manual?: boolean
}

/**
 * useRequest 返回值类型
 * @template T 请求成功时的数据类型
 * @template ResponseData 响应数据类型
 */
export interface UseRequestResult<T, ResponseData = any> {
  /** 请求返回的数据 */
  data: Ref<T | null>
  /** 请求错误信息 */
  error: Ref<AxiosError<ResponseData> | null>
  /** 请求加载状态 */
  loading: Ref<boolean>
  /** 手动执行请求的方法 */
  run: () => Promise<void>
}

/**
 * 通用请求 Hook，接受一个返回 FlatResponseData 的 Promise 函数
 * @template T 请求成功时的数据类型
 * @template ResponseData 响应数据类型
 * @param promiseFunction 返回 Promise<FlatResponseData> 的函数，通常是 createFlatRequest 的调用结果
 * @param options useRequest配置选项
 * @returns 返回包含 data、error、loading 和 run 的对象
 *
 * @example
 * ```typescript
 * // 创建请求实例
 * const request = createFlatRequest()
 *
 * // 自动请求
 * const { data, error, loading, run } = useRequest(() =>
 *   request({ url: '/api/users', method: 'GET' })
 * )
 *
 * // 手动请求
 * const { data, error, loading, run } = useRequest(
 *   () => request({ url: '/api/users', method: 'GET' }),
 *   { manual: true }
 * )
 *
 * // 手动触发请求
 * await run()
 * ```
 */
export default function useRequest<T = any, ResponseData = any>(
  promiseFunction: () => Promise<FlatResponseData<T, ResponseData>>,
  options: UseRequestOptions = {},
): UseRequestResult<T, ResponseData> {
  const { manual = false } = options

  // 使用 loading hook
  const { loading, startLoading, endLoading } = useLoading()

  // 数据状态
  const data = ref<T | null>(null) as Ref<T | null>
  // 错误状态
  const error = ref<AxiosError<ResponseData> | null>(null) as Ref<AxiosError<ResponseData> | null>

  /**
   * 执行请求的方法
   */
  const run = async (): Promise<void> => {
    try {
      // 清除之前的错误
      error.value = null
      // 开始加载
      startLoading()

      // 执行传入的 Promise 函数
      const result = await promiseFunction()

      // 检查请求结果
      if (result.error) {
        // 设置错误
        error.value = result.error
        // 清除数据
        data.value = null
      }
      else {
        // 设置数据
        data.value = result.data
        error.value = null
      }
    }
    catch (err) {
      // 设置错误
      error.value = err as AxiosError<ResponseData>
      // 清除数据
      data.value = null
    }
    finally {
      // 结束加载
      endLoading()
    }
  }

  // 如果不是手动模式，则在组件挂载时自动执行请求
  if (!manual) {
    run()
  }

  return {
    data,
    error,
    loading,
    run,
  }
}
