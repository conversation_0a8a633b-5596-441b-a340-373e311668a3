<script setup lang="ts">
import { Crepe } from '@milkdown/crepe'
import type { Editor } from '@milkdown/kit/core'

import '@milkdown/crepe/theme/common/style.css'
import '@milkdown/crepe/theme/frame.css'

import { editorViewCtx } from '@milkdown/kit/core'
import {
  getMarkdown as _getMarkdown,
  insert as _insert,
  insertPos as _insertPos,
  replaceAll as _replaceAll,
  replaceRang<PERSON> as _replaceRange,
} from '@milkdown/kit/utils'

const props = withDefaults(defineProps<Props>(), {
  value: '',
  disabled: false,
})

const emit = defineEmits<{
  'update:value': [value: string]
  'update:disabled': [value: boolean]
}>()

// 润色
const colorful = '<svg t="1753522644454" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6394" width="200" height="200"><path d="M911.36 819.2c-40.96 17.066667-90.453333-1.706667-107.52-44.373333L658.773333 426.666667c-17.066667-42.666667 1.706667-90.453333 44.373334-109.226667 40.96-17.066667 90.453333 1.706667 107.52 44.373333L955.733333 709.973333c17.066667 42.666667-1.706667 90.453333-44.373333 109.226667zM687.786667 288.426667c-46.08 0-83.626667-37.546667-83.626667-85.333334s37.546667-85.333333 83.626667-85.333333 83.626667 37.546667 83.626666 85.333333-35.84 85.333333-83.626666 85.333334z m54.613333 484.693333c5.12 15.36 5.12 30.72 1.706667 47.786667-11.946667 44.373333-58.026667 69.973333-100.693334 58.026666l-235.52-66.56c-44.373333-11.946667-68.266667-58.026667-56.32-102.4s58.026667-69.973333 100.693334-58.026666l71.68 20.48L413.013333 426.666667 232.106667 851.626667c-18.773333 42.666667-66.56 61.44-107.52 42.666666S63.146667 827.733333 81.92 785.066667l250.88-588.8c6.826667-23.893333 22.186667-44.373333 46.08-54.613334 40.96-18.773333 90.453333 0 109.226667 42.666667L735.573333 750.933333c3.413333 6.826667 5.12 13.653333 6.826667 22.186667z" fill="#3678FD" p-id="6395"></path></svg>'

interface Props {
  value: string
  disabled?: boolean
}

const containerRef = ref()

const editorRef = shallowRef<Editor>()
const crepeRef = shallowRef<Crepe>()

// 添加状态标记，防止循环更新
const isInternalUpdate = ref(false)

function getEditorView() {
  return editorRef.value!.ctx.get(editorViewCtx)
}

function getSelectionCoords() {
  const view = getEditorView()
  const { state } = view
  const { selection } = state

  if (selection.empty) {
    // 获取光标位置
    return {
      left: 0,
      top: 0,
      right: 0,
      bottom: 0,
    }
  }
  else {
    // 获取选区的起始和结束坐标
    const startCoords = view.coordsAtPos(selection.from)
    const endCoords = view.coordsAtPos(selection.to)

    return {
      start: startCoords,
      end: endCoords,
      // 选区的边界框
      boundingBox: {
        left: Math.min(startCoords.left, endCoords.left),
        top: Math.min(startCoords.top, endCoords.top),
        right: Math.max(startCoords.right, endCoords.right),
        bottom: Math.max(startCoords.bottom, endCoords.bottom),
      },
    }
  }
}

async function initEditor() {
  // Choose your preferred theme
  if (crepeRef.value) {
    return
  }

  // Create editor instance
  const crepe = new Crepe({
    root: containerRef.value,
    defaultValue: props.value ?? '',
    features: {
      [Crepe.Feature.ImageBlock]: false,
    },
    featureConfigs: {
      [Crepe.Feature.Toolbar]: {
        buildToolbar: (builder) => {
          const group1 = builder.getGroup('formatting')
          const group2 = builder.getGroup('function')
          const group3 = builder.addGroup('custom', '')
          group3.addItem('perfect', {
            active: _ctx => true,
            icon: colorful,
            onRun: (_ctx) => {
              console.log(getSelectionCoords())
              const selection = getSelection()
              setTimeout(() => {
                replaceRange('# 这是一段替换的文本', selection)
              }, 1000)
            },
          })
          builder.clear();
          [group3, group1, group2].forEach((group) => {
            const { addItem } = builder.addGroup(group.group.key, group.group.label)
            group.group.items.forEach((item) => {
              addItem(item.key, item)
            })
          })
          // Custom toolbar building logic
        },
      },
      [Crepe.Feature.Placeholder]: {
        text: '请输入...',
        mode: 'block',
      },
      [Crepe.Feature.Latex]: {
        katexOptions: {
          throwOnError: false,
          displayMode: true,
        },
      },
    },
  })
  crepeRef.value = crepe
  // 设置是否只读
  crepe.setReadonly(props.disabled)

  crepe.on((listener) => {
    listener.markdownUpdated(() => {
      isInternalUpdate.value = true // 内部更新触发
      emit('update:value', `${getMarkdown()}`)
    })

    listener.updated(() => {

    })

    listener.focus(() => {
    })

    listener.blur(() => {
    })
  })

  // Initialize the editor
  editorRef.value = await crepe.create()
}

onMounted(() => {
  initEditor()
})

onUnmounted(() => {
  crepeRef.value?.destroy()
})

// 更新外部传入的数据
watch(() => props.value, (newValue) => {
  if (isInternalUpdate.value) {
    isInternalUpdate.value = false
    return
  }
  nextTick(() => {
    replaceAll(newValue ?? '')
  })
})

watch(() => props.disabled, (newValue) => {
  setReadOnly(newValue)
})

function getMarkdown() {
  if (!editorRef.value) {
    return ''
  }
  return _getMarkdown()(editorRef.value.ctx)
}

function insert(content: string) {
  if (!editorRef.value) {
    return
  }
  editorRef.value.action(_insert(content))
}

function insertPos(content: string, pos: number, inline: boolean = false) {
  if (!editorRef.value) {
    return
  }
  editorRef.value.action(_insertPos(content, pos, inline))
}

function replaceAll(content: string) {
  if (!editorRef.value) {
    return
  }
  editorRef.value.action(_replaceAll(content))
}

function replaceRange(content: string, range: { from: number, to: number }) {
  if (!editorRef.value) {
    return
  }
  editorRef.value.action(_replaceRange(content, range))
}

function getSelection() {
  return getEditorView().state.selection
}

function setReadOnly(value: boolean) {
  crepeRef.value?.setReadonly(value)
}

defineExpose({
  insert,
  insertPos,
  replaceAll,
  replaceRange,
  getMarkdown,
  getSelectionCoords,
  getSelection,
  setReadOnly,
})
</script>

<template>
  <div ref="containerRef" class="size-full" />
</template>

<style lang="scss">
.milkdown {
  min-height: 100%;

  .ProseMirror {
    padding: 32px 128px;
  }
}
</style>
